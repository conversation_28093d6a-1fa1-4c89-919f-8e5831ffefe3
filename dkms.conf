# dkms.conf: Dynamic Kernel Module Support (DKMS) configuration file for Jo<PERSON>.
#
# The use of DKMS ensures that the Jool kernel modules will be rebuilt
# whenever the kernel is upgraded.
#
# Installation instructions:
#   root@host:~# dkms install /path/to/Jool
#
# You should normally not need to edit any of the lines in this file. The
# meanings of the various directives below are documented in DKMS(8).

PACKAGE_NAME="jool"

# DKMS is quite finicky about the contents of the $PACKAGE_VERSION string. It
# will source dkms.conf every time it performs a task, but if the contents
# $PACKAGE_VERSION changes between runs, it fails to work correctly. Since we
# might dynamically generate the version string (based on output from
# "git describe" or "date"), we make sure to generate it only once and save
# the result in a cache file so it can be re-used later.

# Determine where the actual source tree we're working on is located.
# $BASH_SOURCE holds the full path to dkms.conf, which resides in the
# apex of Jo<PERSON>'s source tree. So we just have to strip off "/dkms.conf".
SRCDIR="${BASH_SOURCE%/dkms.conf}"

# This cache file will hold the generated $PACKAGE_VERSION string.
VERSIONFILE="${SRCDIR}/dkms.package_version.conf"

# If necessary (i.e., if we do not have a cache file at this point), then
# determine the DKMS version string and store it in the cache file.
if test ! -e "${VERSIONFILE}"; then
  # First, determine the main Jool version based on macros in xlat.h
  eval "$(sed -n '/^#define  *JOOL_VERSION_\(MAJOR\|MINOR\|REV\) / {
                    s/^#define  *//;
                    s/  */=/p;
                  }' ${SRCDIR}/src/common/xlat.h)"

  if test ! -z "$JOOL_VERSION_MAJOR" -a ! -z "$JOOL_VERSION_MINOR" -a \
          ! -z "$JOOL_VERSION_REV"; then
    # OK, we successfully determined the main Jool version.
    JOOLVER="${JOOL_VERSION_MAJOR}.${JOOL_VERSION_MINOR}.${JOOL_VERSION_REV}"
  else
    # We failed to determine the main Jool version. Maybe xlat.h changed
    # syntax or the JOOL_VERSION_* macros were moved elsewhere? In any case
    # just use a dummy version string (which must start with a digit,
    # otherwise "dkms mkdeb" won't work).
    JOOLVER="0unknown"
  fi

  # If we're working on a Git checkout, we'll also want to include output
  # from "git describe" in the DKMS version string.
  if test -d "${SRCDIR}/.git"; then
    # We need to replace '-' with '.' in order to make "dkms mkrpm" and
    # "dkms mkdeb" to work correctly.
    GITVER="$(git -C "${SRCDIR}" describe --always | tr - . 2>/dev/null)"
    # In the unlikely event that "git describe" fails (maybe the admin
    # has uninstalled git?), we'll use a date-based string instead.
    test -z "${GITVER}" && GITVER="$(date +%Y%m%d)"
    # Prepend ".git." to Git version string so its origin is clear.
    GITVER=".git.${GITVER}"
  fi

  # All done - now store the generated version string for later use.
  echo "# Automatically generated from dkms.conf on $(date -R)" > \
         "${VERSIONFILE}"
  echo "PACKAGE_VERSION=${JOOLVER}${GITVER}" >> "${VERSIONFILE}"
fi

# At this point we should be able to read in the previously generated
# $PACKAGE_VERSION string from the cache file. If this fails, DKMS will
# simply fail and complain that the dkms.conf did not set $PACKAGE_VERSION.
. "${VERSIONFILE}"

# This instructs DKMS to rebuild the Jool kernel modules when necessary,
# such as after a kernel upgrade.
AUTOINSTALL="yes"

MAKE[0]="make -C ${kernel_source_dir} M=${dkms_tree}/${PACKAGE_NAME}/${PACKAGE_VERSION}/build/src/mod/common modules \
      && make -C ${kernel_source_dir} M=${dkms_tree}/${PACKAGE_NAME}/${PACKAGE_VERSION}/build/src/mod/nat64 modules \
      && make -C ${kernel_source_dir} M=${dkms_tree}/${PACKAGE_NAME}/${PACKAGE_VERSION}/build/src/mod/siit modules"

CLEAN="make -C ${dkms_tree}/${PACKAGE_NAME}/${PACKAGE_VERSION}/build/src/mod clean"

BUILT_MODULE_NAME[0]="jool_common"
BUILT_MODULE_LOCATION[0]="src/mod/common/"
DEST_MODULE_LOCATION[0]="/extra/"

BUILT_MODULE_NAME[1]="jool"
BUILT_MODULE_LOCATION[1]="src/mod/nat64/"
DEST_MODULE_LOCATION[1]="/extra/"

BUILT_MODULE_NAME[2]="jool_siit"
BUILT_MODULE_LOCATION[2]="src/mod/siit/"
DEST_MODULE_LOCATION[2]="/extra/"
