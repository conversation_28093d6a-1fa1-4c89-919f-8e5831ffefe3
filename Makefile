#
# gpl/usr/jool/Makefile
#
#
# Copyright (c) 2022 CradlePoint, Inc. <www.cradlepoint.com>.
# All rights reserved.
#
# This file contains confidential information of CradlePoint, Inc. and your 
# use of this file is subject to the CradlePoint Software License Agreement 
# distributed with this file. Unauthorized reproduction or distribution of 
# this file is subject to civil and criminal penalties.
#

ROOT ?= $(abspath ../..)
include $(ROOT)/Makefile.config

SUBS := src
SUBS_CLEAN = $(patsubst %,%-clean, $(SUBS))

default: all 

all: $(SUBS)

install: $(SUBS)

clean: $(SUBS:=-clean)

$(SUBS): .PHONY
	$(MAKE) -C $@ -j$(JOBS)
	$(MAKE) -C $@ install

$(SUBS_CLEAN): .PHONY
	$(MAKE) -C $(patsubst %-clean,%, $@) clean

.PHONY:
