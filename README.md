# Update

jool.mx is no longer maintained. Please use https://nicmx.github.io/Jool instead.

**I REPEAT: jool.mx IS (AND WILL PROBABLY FOREVER BE) OUTDATED. DO NOT QUERY IT ANYMORE. PLEASE UPDATE YOUR BOOKMARKS.**

# [Jo<PERSON>](https://nicmx.github.io/Jool)

An [SIIT and a NAT64](https://nicmx.github.io/Jool/en/intro-xlat.html) for Linux.

## Documentation

[See here](https://nicmx.github.io/Jool/en/documentation.html). You can also [generate the docs yourself](docs/).

You might also want to see [contact info](https://nicmx.github.io/Jool/en/contact.html).

## Credits

### NIC-Mx

* Ing. Jorge <PERSON>
* Ing. <PERSON> - <EMAIL>
* Ing. <PERSON>
* Ing. [<PERSON>](https://github.com/dhfelix) - d<PERSON>nan<PERSON>@nic.mx
* Ing. [<PERSON>](https://github.com/ydahhrk)
* Ing. Cristóbal Alfonso de León Benítez - <EMAIL>
* Ing. Roberto Olivas Alarcón - <EMAIL>
* Edgar Martín Rodríguez Bernal - <EMAIL>

### ITESM representatives

* Dr. Juan Arturo Nolazco - <EMAIL>
* Ing. Martha Sordia - <EMAIL>

### ITESM students

* [Abraham Baez](https://github.com/basuam) - <EMAIL>
* [Adrian González](https://github.com/Adrian2112) - <EMAIL>
* Alan Villela López - <EMAIL>
* [Angel Cazares](https://github.com/legionAngel) - <EMAIL>
* Armando Cifuentes González - <EMAIL>
* [David Valenzuela](https://github.com/davidvrdz) - <EMAIL>
* Ing. Elvia Patricia Barrón Cano - <EMAIL>
* [Gabriel Chavez](https://github.com/chavezgu) - <EMAIL>
* Jose Vicente Ramirez - <EMAIL>
* [Juan Antonio Osorio](https://github.com/JAORMX) - <EMAIL>
* Juan Francisco Barragán Cantú - <EMAIL>
* [Luis Fernando Hinojosa](https://github.com/luion) - <EMAIL>
* Manuel Aude - <EMAIL>
* Mario Gerardo Trevinho - <EMAIL>
* [Miguel Alejandro González](https://github.com/magg) - <EMAIL>
* [Nixa Jayu Gpe Rodríguez Dagnino](https://github.com/NixaDagnino)
* [Ramiro Nava](https://github.com/ramironava) - <EMAIL>
* [Roberto Aceves](https://github.com/robertoaceves) - <EMAIL>
* [Roberto Martínez Beltrán](https://github.com/ryuzak) - <EMAIL>

### Special Thanks (contributing users)

* [airsnail](https://github.com/airsnail)
* [Andreas Urke](https://github.com/arurke)
* Andreas Rammhold
* [Dan Lüdtke](https://www.danrl.com)
* Eric Gamess
* [Hidekazu Tadokoro](https://github.com/tadokoro)
* [ipclouds](https://github.com/ipclouds)
* Jan Pokorny - FIT VUTBR
* [Masaya YAMAMOTO](https://github.com/pandax381)
* [Michael Richardson](http://www.sandelman.ca/mcr)
* [Molly Miller](https://github.com/sysvinit)
* [paradon](https://github.com/paradon)
* [Philar Law](https://github.com/philar)
* [Pier Carlo Chiodi](https://pierky.com/)
* [Ricardo Salveti](https://github.com/rsalveti) - <EMAIL>
* [Sander Steffann](https://github.com/steffann) - <EMAIL>
* [Stanislav Bogatyrev](https://github.com/realloc)
* [tbe](https://github.com/tbe)
* [techmotive](https://github.com/techmotive)
* Terry Froy
* [Tore Anderson](https://github.com/toreanderson)
* Washam Fan

### License Disclaimer

```
Jool - SIIT and NAT64 for Linux
Copyright (C) 2025  NIC Mexico <<EMAIL>>

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
version 2, as published by the Free Software Foundation.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin Street, Fifth Floor, Boston, MA 02110-1301, USA.
```
