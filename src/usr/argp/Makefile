#
# gpl/usr/jool/src/usr/argp/Makefile
#
#
# Copyright (c) 2022 CradlePoint, Inc. <www.cradlepoint.com>.
# All rights reserved.
#
# This file contains confidential information of CradlePoint, Inc. and your 
# use of this file is subject to the CradlePoint Software License Agreement 
# distributed with this file. Unauthorized reproduction or distribution of 
# this file is subject to civil and criminal penalties.
#

ROOT ?= $(abspath ../../../../..)
include $(ROOT)/Makefile.config

CFLAGS  += -Wall -pedantic -std=gnu11 -O2 -I../..

ARGP_OBJS := \
	command.o \
	log.o \
	requirements.o \
	wargp.o \
	dns.o \
	main.o \
	userspace-types.o \
	xlator_type.o \
	wargp/address.o \
	wargp/eamt.o \
	wargp/instance.o \
	wargp/session.o \
	wargp/bib.o \
	wargp/file.o \
	wargp/joold.o \
	wargp/stats.o \
	wargp/denylist4.o \
	wargp/global.o \
	wargp/pool4.o

DEFS := -DPACKAGE_NAME=\"Jool\" -DPACKAGE_TARNAME=\"jool\" -DPACKAGE_VERSION=\"4.1.7\" -DPACKAGE_STRING=\"Jool\ 4.1.7\" -DPACKAGE_BUGREPORT=\"<EMAIL>\" -DPACKAGE_URL=\"\" -DPACKAGE=\"jool\" -DVERSION=\"4.1.7\" -DSTDC_HEADERS=1 -DHAVE_SYS_TYPES_H=1 -DHAVE_SYS_STAT_H=1 -DHAVE_STDLIB_H=1 -DHAVE_STRING_H=1 -DHAVE_MEMORY_H=1 -DHAVE_STRINGS_H=1 -DHAVE_INTTYPES_H=1 -DHAVE_STDINT_H=1 -DHAVE_ARPA_INET_H=1 -DHAVE_UNISTD_H=1 -DHAVE__BOOL=1 -DHAVE_INET_NTOA=1 -DHAVE_MEMSET=1 -DHAVE_STRCASECMP=1 -DHAVE_STRTOL=1 -DHAVE_DLFCN_H=1

install: all

all:
	make libjoolargp.a;

libjoolargp.a : ${ARGP_OBJS}
	$(AR) cr $@ $^

%.o: %.c
	${CC} ${CFLAGS} ${DEFS} -c -o $@ $<;

clean:
	rm -f *.o
	rm -f wargp/*.o
	rm -f *.a

.PHONY: libjoolargp.a
