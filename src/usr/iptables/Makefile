#
# gpl/usr/jool/src/usr/iptables/Makefile
#
#
# Copyright (c) 2022 CradlePoint, Inc. <www.cradlepoint.com>.
# All rights reserved.
#
# This file contains confidential information of CradlePoint, Inc. and your 
# use of this file is subject to the CradlePoint Software License Agreement 
# distributed with this file. Unauthorized reproduction or distribution of 
# this file is subject to civil and criminal penalties.
#

ROOT ?= $(abspath ../../../../..)
include $(ROOT)/Makefile.config

CFLAGS  += -Wall -pedantic -std=gnu11 -O2 -I../..
#DEFAULT_CFLAGS += $(shell pkg-config xtables --cflags)

XTABLES_SO_DIR = /usr/lib/xtables

all:
	make libxt_JOOL_SIIT.so;
	make libxt_JOOL.so;
install:
	mkdir -p ${DESTDIR}${XTABLES_SO_DIR}
	cp *.so ${DESTDIR}${XTABLES_SO_DIR}
uninstall:
	rm -f ${DESTDIR}${XTABLES_SO_DIR}/libxt_JOOL_SIIT.so
	rm -f ${DESTDIR}${XTABLES_SO_DIR}/libxt_JOOL.so
lib%.so: lib%.o
	${CC} -shared -fPIC ${LDFLAGS} -o $@ $^;
lib%.o: lib%.c
	${CC} ${CFLAGS} -D_INIT=lib$*_init -fPIC -c -o $@ $<;
clean distclean maintainer-clean:
	rm -f *.so
distdir:
	mkdir -p ${distdir}
	cp *.c *.man Makefile ${distdir}
