#
# gpl/usr/jool/src/mod/common/Makefile
#
# Copyright (c) 2022 CradlePoint, Inc. <www.cradlepoint.com>.
# All rights reserved.
#
# This file contains confidential information of CradlePoint, Inc. and your
# use of this file is subject to the CradlePoint Software License Agreement
# distributed with this file. Unauthorized reproduction or distribution of
# this file is subject to civil and criminal penalties.
#

ROOT ?= $(abspath ../../../../..)
include $(ROOT)/Makefile.config

KBUILD_MODPOST_WARN := yes
unexport CC CPP LD CFLAGS CPPFLAGS LDFLAGS AR RANLIB

default: all

all:
	$(MAKE) -C $(CPKERN) M=$(CURDIR) KBUILD_MODPOST_WARN=$(KBUILD_MODPOST_WARN)

install:
	$(MAKE) -C $(CPKERN) M=$(CURDIR) INSTALL_MOD_PATH=$(DESTDIR) modules_install

clean:
	$(MAKE) -C $(CPKERN) M=$(CURDIR) clean

checkpatch:
	$(CPKERN)/scripts/checkpatch.pl --no-tree --no-signoff --file *.c *.h  --ignore Long_Line_String --ignore Long_Line --ignore Long_Line_Comment --show-types


.PHONY:clean
