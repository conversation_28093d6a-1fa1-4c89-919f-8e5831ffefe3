.\" Manpage for graybox's userspace app.
.\" Report <NAME_EMAIL>.

.TH graybox 7 2016-09-01 v1.0.0 "Graybox kernel module's client"

.SH NAME
graybox

.SH DESCRIPTION
The whole idea behing the Graybox framework is to automate full packet translation tests.
.P
.RB "Basically, a tester has a bunch of tests. In the simplest case, each test consists of three packets: The " test " packet, the " expected " packet and the " actual " packet."
.P
This happens:
.P
.RB "1. The user sends " expected " to the Graybox Kernel Module (GKM). GKM starts 'expecting' this packet for a user-defined amount of time."
.br
.RB "2. The user sends " test " to the GKM."
.br
.RB "3. The GKM puts " test " on the network (as is)."
.br
.RB "4. Somebody (usually <PERSON><PERSON>) 'answers' " test ". This answer is the " actual " packet."
.br
.RB "5. GKM confirms " actual " equals " expected ". The test is considered a success if this is the case."
.P
This allows us to prooftest large Jool pipelines without worrying about its innards. On the other hand, they are not high-level tests (in that we still worry about network and transport protocols' innards), so I felt they are neither "black" nor "white" box tests (hence the name). (Strictly speaking, this might not adhere properly to the formal definition of test colorings, but it doesn't matter much now.)
.P
The Graybox Userspace Client (normally "GUC" but referred to in this document as just "graybox") is the bridge between the user and the GKM.

.SH SYNTAX
.RI "graybox expect add " <expected> " [" <exceptions> "]
.br
.RB "	Add a packet to the " expected " packets list."
.P
graybox expect flush
.br
.RB "	Purge the " expected " packets list."
.P
.RI "graybox send " <test>
.br
.RB "	Send a " test " packet."
.P
graybox stats display
.br
	Show test statistics.
.P
graybox stats flush
.br
	Reset test statistics.

.SH ARGUMENTS
.SS <expected>
.RB "Name of a file containing the " expected " packet. The GKM will queue and wait for this packet. (" Expected " packets should arrive in the same order as they are defined.)"
.P
The packet must be literal and typically include headers from the network layer and above. In other words, the file can for example contain 40 bytes of raw IPv6 header, then 20 bytes of raw TCP header and then 5 bytes of arbitrary payload.
.P
.RB "Lower layer headers (data link and below) are removed from " actual " before the comparison takes place."
.SS <exceptions>
.RB "Comma-separated list of byte indices that should not be included in the " actual - expected " comparison. (The indices are zero-based.)"
.P
.RB "This exists because some fields (such as IPv4's identification value) are defined randomly in some scenarios and/or can be overwritten by some misbehaving kernels. These situations often do not hinder standards compliance, but yield annoying false positives during simple " expected - actual " comparisons anyway."
.P
So, for example, to ignore the translated IPv4 identification during a 6-to-4 translation, define <exceptions> as "4,5" (Because identification is the fifth and sixth bytes of the packet).
.SS <test>
.RB "Name of a file containing the " test " packet. The GKM will simply try to send this packet as is."
.P
The packet must be literal and typically include headers from the network layer and above. In other words, the file can for example contains 20 bytes of raw IPv4 header, then 20 bytes of raw TCP header and then 5 bytes of arbitrary payload.
.P
Lower layer headers (data link and below) will be autogenerated.
.P
This packet must be valid to some extent, since the kernel cannot fetch a packet unless it can at least route it first.

.SH EXIT STATUS
Zero on success, non-zero on failure.
.P
If the command is `stats display`, non-zero will also be returned if there was at least one test failure or queued packet.

.SH OUTPUT
.RB "A test is considered a success when " expected " equals " actual " (barring defined exceptions)."
.br
.RB "A test is considered a failure when " expected " does not equal " actual " (barring defined exceptions)."
.br
.RB "A test is considered 'queued' when " actual " never arrived. (" Expected " is still queued.)"
.P
If you flush the expected packet list when there are still packets queued, the packets are removed from the list but they are still considered "queued" as far as the stat is concerned.


.SH EXAMPLES
Modprobe the GKM first: (If you didn't install it, use the `insmod` variant.)
.br
	$ modprobe graybox
.P
Now send a stray packet:
.br
	$ graybox send test.pkt
.P
Stat a success if packet "1.pkt" is translated and bounced back as "2.pkt":
.br
	$ graybox expect add 2.pkt
.br
	$ graybox send 1.pkt
.P
Stat a success if packet "1.pkt" is translated and bounced back as "2.pkt", but ignore the identification field and the UDP checksum:
.br
	$ graybox expect add 2.pkt 4,5,26,27
.br
	$ graybox send 1.pkt
.P
The packet which has been fragmented as "1.pkt", "2.pkt" and "3.pkt" should be translated as "4.pkt" and "5.pkt":
.br
	$ graybox expect add 4.pkt
.br
	$ graybox expect add 5.pkt
.br
	$ graybox send 1.pkt
.br
	$ graybox send 2.pkt
.br
	$ graybox send 3.pkt
.P
Show test results:
.br
	$ graybox stats display
.P
We're done; remove the GKM. Keeping it is (at least) a security vulnerability: (If you didn't install it, use the `rmmod` variant.)
.br
	$ modprobe -r graybox
.br

.SH AVAILABILITY
Linux is the only OS in which this program makes sense.
.br
Kernels 3.2.0 and up.

.SH AUTHOR
NIC Mexico & ITESM

.SH REPORTING BUGS
Our issue tracker is https://github.com/NICMx/Jool/issues.
If you want to mail us instead, use <EMAIL>.

.SH COPYRIGHT
Copyright 2016 NIC Mexico.
.br
License: GPLv2 (GNU GPL version 2)
.br
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.

.SH SEE ALSO
https://www.jool.mx
