<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="svg2"
   version="1.1"
   inkscape:version="0.48.4 r9939"
   width="42"
   height="24"
   sodipodi:docname="email.svg"
   style="enable-background:new">
  <metadata
     id="metadata8">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <defs
     id="defs6">
    <linearGradient
       id="linearGradient3799">
      <stop
         style="stop-color:#ef4027;stop-opacity:1;"
         offset="0"
         id="stop3801" />
      <stop
         style="stop-color:#cf112e;stop-opacity:1;"
         offset="1"
         id="stop3803" />
    </linearGradient>
    <linearGradient
       id="linearGradient3836">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop3838" />
      <stop
         style="stop-color:#000000;stop-opacity:0;"
         offset="1"
         id="stop3840" />
    </linearGradient>
    <linearGradient
       id="linearGradient3820">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop3822" />
      <stop
         style="stop-color:#000000;stop-opacity:0;"
         offset="1"
         id="stop3824" />
    </linearGradient>
    <linearGradient
       id="linearGradient2991">
      <stop
         style="stop-color:#fefefe;stop-opacity:1;"
         offset="0"
         id="stop2993" />
      <stop
         style="stop-color:#d3d3d3;stop-opacity:1;"
         offset="1"
         id="stop2995" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2991"
       id="linearGradient2997"
       x1="67.400352"
       y1="15.82839"
       x2="93.761993"
       y2="15.82839"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(-49.459746,-4.4851695)" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2991"
       id="linearGradient3844"
       x1="0"
       y1="16"
       x2="32"
       y2="16"
       gradientUnits="userSpaceOnUse" />
    <filter
       inkscape:collect="always"
       id="filter3005"
       color-interpolation-filters="sRGB">
      <feBlend
         inkscape:collect="always"
         mode="multiply"
         in2="BackgroundImage"
         id="feBlend3007" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2991"
       id="linearGradient3020"
       gradientUnits="userSpaceOnUse"
       x1="0"
       y1="16"
       x2="32"
       y2="16" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2991"
       id="linearGradient3797"
       gradientUnits="userSpaceOnUse"
       x1="0"
       y1="16"
       x2="32"
       y2="16"
       gradientTransform="translate(12,-4)" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3799"
       id="linearGradient3805"
       x1="4"
       y1="12"
       x2="8"
       y2="12"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2991"
       id="linearGradient3783"
       x1="12"
       y1="12"
       x2="44"
       y2="12"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3799"
       id="linearGradient3785"
       x1="1.5"
       y1="12"
       x2="10.5"
       y2="12"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient2991"
       id="linearGradient3788"
       gradientUnits="userSpaceOnUse"
       x1="12"
       y1="12"
       x2="40"
       y2="12" />
  </defs>
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1920"
     inkscape:window-height="1028"
     id="namedview4"
     showgrid="true"
     inkscape:zoom="22.627416"
     inkscape:cx="21.489609"
     inkscape:cy="10.013296"
     inkscape:window-x="0"
     inkscape:window-y="24"
     inkscape:window-maximized="1"
     inkscape:current-layer="layer2">
    <inkscape:grid
       type="xygrid"
       id="grid3770"
       empspacing="5"
       visible="true"
       enabled="true"
       snapvisiblegridlinesonly="true"
       spacingx="2px"
       spacingy="2px" />
  </sodipodi:namedview>
  <g
     inkscape:groupmode="layer"
     id="layer2"
     inkscape:label="drawing"
     style="display:inline">
    <path
       style="fill:url(#linearGradient3788);stroke:none"
       d="m 12,4 28,0 0,16 -28,0 z"
       id="rect3781"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="ccccc" />
    <path
       style="fill:none;stroke:#b3b3b3;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       d="M 12,4 26,16 40,4"
       id="path3772"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="ccc" />
    <path
       style="fill:none;stroke:#b3b3b3;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       d="m 12,20 9.322,-7.99"
       id="path3780"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#b3b3b3;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       d="M 30.678,12.01 40,20"
       id="path3776"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="cc" />
    <path
       style="fill:none;stroke:#b3b3b3;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dashoffset:0"
       d="m 12,4 28,0 0,16 -28,0 z"
       id="rect3009"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="ccccc" />
    <path
       style="fill:url(#linearGradient3785);stroke:#aa0000;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
       d="m 6,10 0,-2 4,4 -4,4 0,-2 -4,0 0,-4 z"
       id="path3022"
       inkscape:connector-curvature="0" />
  </g>
</svg>
