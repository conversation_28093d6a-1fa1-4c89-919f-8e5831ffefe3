---
language: en
layout: default
category: Download
title: Download
url-dl-old: https://github.com/NICMx/releases/raw/master/Jool
url-dl: https://github.com/NICMx/Jool/releases/download
---

# Downloads

- **Bold** marks the most recommended version(s).
- Normal font signals recommended fallback versions in case the previous option fails you ([bug reports welcomed]({{ site.repository-url }}/issues)).
- <del>Striked</del> versions are not recommended (either because they're too old or have known critical bugs).

## 4.2.x

Jool 4.2 is a compliant SIIT, Stateful NAT64 and MAP-T.

| Release Date | Version | .tar.gz | .tar.gz Signature | Git commit | .deb |
|--------------|---------|---------|-------------------|------------|------|
| 2021-02-19   | 4.2.0-rc2 | [Download]({{ page.url-dl }}/v4.2.0-rc2/jool-4.2.0.rc2.tar.gz) | [Signature]({{ page.url-dl }}/v4.2.0-rc2/jool-4.2.0.rc2.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.2.0-rc2" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.2.0-rc2/jool-dkms_4.2.0.rc2-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.2.0-rc2/jool-tools_4.2.0.rc2-1_amd64.deb) (amd64 only) |
| 2020-12-24   | 4.2.0-rc1 | [Download]({{ page.url-dl }}/v4.2.0-rc1/jool-4.2.0.rc1.tar.gz) | [Signature]({{ page.url-dl }}/v4.2.0-rc1/jool-4.2.0.rc1.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.2.0-rc1" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.2.0-rc1/jool-dkms_4.2.0.rc1-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.2.0-rc1/jool-tools_4.2.0.rc1-1_amd64.deb) (amd64 only) |

"rc" stands for "Release Candidate."

## 4.1.x

Jool 4.1 is a [compliant SIIT and Stateful NAT64](intro-jool.html#compliance).

Currently, 4.1.7 is the most mature version of Jool.

| Release Date | Version | .tar.gz | .tar.gz Signature | Git commit | .deb |
|--------------|---------|---------|-------------------|------------|------|
| 2022-01-27   | **4.1.7** | [Download]({{ page.url-dl }}/v4.1.7/jool-4.1.7.tar.gz) | [Signature]({{ page.url-dl }}/v4.1.7/jool-4.1.7.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.1.7" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.1.7/jool-dkms_4.1.7-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.1.7/jool-tools_4.1.7-1_amd64.deb) (amd64 only) |
| 2021-12-10   | **4.1.6** | [Download]({{ page.url-dl }}/v4.1.6/jool-4.1.6.tar.gz) | [Signature]({{ page.url-dl }}/v4.1.6/jool-4.1.6.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.1.6" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.1.6/jool-dkms_4.1.6-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.1.6/jool-tools_4.1.6-1_amd64.deb) (amd64 only) |
| 2021-02-19   | 4.1.5 | [Download]({{ page.url-dl }}/v4.1.5/jool-4.1.5.tar.gz) | [Signature]({{ page.url-dl }}/v4.1.5/jool-4.1.5.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.1.5" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.1.5/jool-dkms_4.1.5-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.1.5/jool-tools_4.1.5-1_amd64.deb) (amd64 only) |
| 2020-10-07   | 4.1.4 | [Download]({{ page.url-dl }}/v4.1.4/jool-4.1.4.tar.gz) | [Signature]({{ page.url-dl }}/v4.1.4/jool-4.1.4.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.1.4" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.1.4/jool-dkms_4.1.4-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.1.4/jool-tools_4.1.4-1_amd64.deb) (amd64 only) |
| 2020-09-02   | 4.1.3 | [Download]({{ page.url-dl }}/v4.1.3/jool-4.1.3.tar.gz) | [Signature]({{ page.url-dl }}/v4.1.3/jool-4.1.3.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.1.3" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.1.3/jool-dkms_4.1.3-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.1.3/jool-tools_4.1.3-1_amd64.deb) (amd64 only) |
| 2020-07-22   | <del>4.1.2</del> | [Download]({{ page.url-dl }}/v4.1.2/jool-4.1.2.tar.gz) | [Signature]({{ page.url-dl }}/v4.1.2/jool-4.1.2.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.1.2" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.1.2/jool-dkms_4.1.2-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.1.2/jool-tools_4.1.2-1_amd64.deb) (amd64 only) |
| 2020-07-01   | <del>4.1.1</del> | [Download]({{ page.url-dl }}/v4.1.1/jool-4.1.1.tar.gz) | [Signature]({{ page.url-dl }}/v4.1.1/jool-4.1.1.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.1.1" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.1.1/jool-dkms_4.1.1-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.1.1/jool-tools_4.1.1-1_amd64.deb) (amd64 only) |
| 2020-06-16   | <del>4.1.0</del> | [Download]({{ page.url-dl }}/v4.1.0/jool-4.1.0.tar.gz) | [Signature]({{ page.url-dl }}/v4.1.0/jool-4.1.0.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.1.0" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.1.0/jool-dkms_4.1.0-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.1.0/jool-tools_4.1.0-1_amd64.deb) (amd64 only) |

[This](http://keys.gnupg.net/pks/lookup?op=get&search=0x72160FD57B242967) is my public key. It is not yet certified, so the Signature column is mostly just theater for now.

## 4.0.x

Jool 4.0 is a generally compliant SIIT and Stateful NAT64.

| Release Date | Version | .tar.gz | .tar.gz Signature | Git commit | .deb |
|--------------|---------|---------|-------------------|------------|------|
| 2020-05-05   | <del>4.0.9</del> | [Download]({{ page.url-dl }}/v4.0.9/jool-4.0.9.tar.gz) | [Signature]({{ page.url-dl }}/v4.0.9/jool-4.0.9.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.0.9" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.0.9/jool-dkms_4.0.9-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.0.9/jool-tools_4.0.9-1_amd64.deb) (amd64 only) |
| 2020-03-30   | <del>4.0.8</del> | [Download]({{ page.url-dl }}/v4.0.8/jool-4.0.8.tar.gz) | [Signature]({{ page.url-dl }}/v4.0.8/jool-4.0.8.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.0.8" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.0.8/jool-dkms_4.0.8-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.0.8/jool-tools_4.0.8-1_amd64.deb) (amd64 only) |
| 2019-12-17   | <del>4.0.7</del> | [Download]({{ page.url-dl }}/v4.0.7/jool-4.0.7.tar.gz) | [Signature]({{ page.url-dl }}/v4.0.7/jool-4.0.7.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.0.7" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.0.7/jool-dkms_4.0.7-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.0.7/jool-tools_4.0.7-1_amd64.deb) (amd64 only) |
| 2019-10-24   | <del>4.0.6</del> | [Download]({{ page.url-dl }}/v4.0.6/jool-4.0.6.tar.gz) | [Signature]({{ page.url-dl }}/v4.0.6/jool-4.0.6.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.0.6" target="_blank">Link</a> | [Kernel modules]({{ page.url-dl }}/v4.0.6/jool-dkms_4.0.6-1_all.deb)<br />[Userspace tools]({{ page.url-dl }}/v4.0.6/jool-tools_4.0.6-1_amd64.deb) (amd64 only) |
| 2019-08-20   | <del>4.0.5</del> | [Download]({{ page.url-dl }}/v4.0.5/jool-4.0.5.tar.gz) | [Signature]({{ page.url-dl }}/v4.0.5/jool-4.0.5.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.0.5" target="_blank">Link</a> | - |
| 2019-07-31   | <del>4.0.4</del> | [Download]({{ page.url-dl }}/v4.0.4/jool-4.0.4.tar.gz) | [Signature]({{ page.url-dl }}/v4.0.4/jool-4.0.4.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.0.4" target="_blank">Link</a> | - |
| 2019-07-19   | <del>4.0.3</del> | [Download]({{ page.url-dl }}/v4.0.3/jool-4.0.3.tar.gz) | [Signature]({{ page.url-dl }}/v4.0.3/jool-4.0.3.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.0.3" target="_blank">Link</a> | - |
| 2019-07-11   | <del>4.0.2</del> | [Download]({{ page.url-dl }}/v4.0.2/jool-4.0.2.tar.gz) | [Signature]({{ page.url-dl }}/v4.0.2/jool-4.0.2.tar.gz.asc) | <a href="{{ site.repository-url }}/tree/v4.0.2" target="_blank">Link</a> | - |
| 2019-04-26   | <del>4.0.1</del> | [Download]({{ page.url-dl }}/v4.0.1/jool_4.0.1.tar.gz) | - | <a href="{{ site.repository-url }}/tree/v4.0.1" target="_blank">Link</a> | - |
| 2019-01-17   | <del>4.0.0</del> | [Download]({{ page.url-dl }}/v4.0.0/jool_4.0.0.tar.gz) | - | <a href="{{ site.repository-url }}/tree/v4.0.0" target="_blank">Link</a> | - |
| 2019-01-09   | <del>4.0.0-rc5</del> | [Download]({{ page.url-dl }}/v4.0.0-rc5/jool_4.0.0-rc5.tar.gz) | - | <a href="{{ site.repository-url }}/tree/v4.0.0-rc5" target="_blank">Link</a> | - |
| 2019-01-04   | <del>3.6.0-rc4</del> | [Download]({{ page.url-dl }}/v3.6.0-rc4/jool_3.6.0-rc4.tar.gz) | - | <a href="{{ site.repository-url }}/tree/v3.6.0-rc4" target="_blank">Link</a> | - |
| 2018-12-26   | <del>3.6.0-rc3</del> | [Download]({{ page.url-dl-old }}/jool_3.6.0-rc3.tar.gz) | - | <a href="{{ site.repository-url }}/tree/v3.6.0-rc3" target="_blank">Link</a> | - |
| 2018-12-14   | <del>3.6.0-rc2</del> | [Download]({{ page.url-dl-old }}/jool_3.6.0-rc2.tar.gz) | - | <a href="{{ site.repository-url }}/tree/v3.6.0-rc2" target="_blank">Link</a> | - |
| 2018-11-24   | <del>3.6.0-rc1</del> | [Download]({{ page.url-dl-old }}/jool_3.6.0-rc1.tar.gz) | - | <a href="{{ site.repository-url }}/tree/v3.6.0-rc1" target="_blank">Link</a> | - |

## 3.5.x

Jool 3.5 is a generally compliant SIIT and Stateful NAT64.

3.5.8 is the latest version.

> Note! This site's documentation pertains to the new 4.0 series.
>
> You can download a copy of the Jool 3.5 documentation [here]({{ page.url-dl-old }}/Jool-3.5-doc.zip).

| Release Date | Version | .zip | .tar.gz | Git commit |
|--------------|---------|------|---------|------------|
| 2018-04-26 | <del>3.5.8</del> | [Download]({{ page.url-dl }}/v3.5.8/Jool-3.5.8.zip) | - | <a href="{{ site.repository-url }}/tree/v3.5.8" target="_blank">Link</a> |
| 2018-05-04 | <del>3.5.7</del> | [Download]({{ page.url-dl-old }}/Jool-3.5.7.zip) | - | <a href="{{ site.repository-url }}/tree/v3.5.7" target="_blank">Link</a> |
| 2018-01-16 | <del>3.5.6</del> | <del>[Download]({{ page.url-dl-old }}/Jool-3.5.6.zip)</del> | - | <a href="{{ site.repository-url }}/tree/v3.5.6" target="_blank">Link</a> |
| 2017-11-23 | <del>3.5.5</del> | <del>[Download]({{ page.url-dl-old }}/Jool-3.5.5.zip)</del> | - | <a href="{{ site.repository-url }}/tree/v3.5.5" target="_blank">Link</a> |
| 2017-07-25 | <del>3.5.4</del> | <del>[Download]({{ page.url-dl-old }}/Jool-3.5.4.zip)</del> | - | <a href="{{ site.repository-url }}/tree/v3.5.4" target="_blank">Link</a> |
| 2017-03-09 | <del>3.5.3</del> | <del>[Download]({{ page.url-dl-old }}/Jool-3.5.3.zip)</del> | - | <a href="{{ site.repository-url }}/tree/v3.5.3" target="_blank">Link</a> |
| 2016-12-06 | <del>3.5.2</del> | <del>[Download]({{ page.url-dl-old }}/Jool-3.5.2.zip)</del> | - | <a href="{{ site.repository-url }}/tree/v3.5.2" target="_blank">Link</a> |
| 2016-12-02 | <del>3.5.1</del> | <del>[Download]({{ page.url-dl-old }}/Jool-3.5.1.zip)</del> | - | <a href="{{ site.repository-url }}/tree/v3.5.1" target="_blank">Link</a> |
| 2016-10-07 | <del>3.5.0</del> | <del>[Download]({{ page.url-dl-old }}/Jool-3.5.0.zip)</del> | - | <a href="{{ site.repository-url }}/tree/v3.5.0" target="_blank">Link</a> |
| 2018-05-28 | 3.5 - 3.6 development hybrid | - | [Download]({{ page.url-dl-old }}/jool_3.5.255.tar.gz) | <a href="{{ site.repository-url }}/tree/976a08dbbd85d22220ef846f12855592c7236448" target="_blank">Link</a> |

## Older releases

Versions 3.4 and below are no longer supported because of licensing issues. You can still find the old releases (and corresponding documentation) in the [release repository](https://github.com/NICMx/releases/tree/master/Jool).

